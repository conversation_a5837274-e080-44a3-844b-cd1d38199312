memory:
  priority_modules:  
    - "src/memory-bank/*.md"  # 项目架构、当前进展、详细设计信息
    - "src/memory-bank/themeCookbook.md"      # 主题开发手册
    - "src/memory-bank/docCookbook.md"      # 主题开发文案-模型映射功能
    - "src/memory-bank/facade_service_role.md"      # 门面服务角色文档
    - "src/memory-bank/OrderSearchServiceRPC.md"      # RPC服务文档
    - "src/memory-bank/systemPatterns.md"      # 系统模式和技术上下文
    - "src/memory-bank/techContext.md"      # 系统模式和技术上下文
    - "src/memory-bank/progress.md"      # 项目进度和上下文
    - "src/memory-bank/productContext.md"      # 项目进度和上下文
  retention_days: 30      # 记忆保留周期
  storage:
    hot: chromadb       # 高频记忆使用向量数据库
    cold: neo4j         # 低频架构知识用图数据库
  cache_size: 700MB     # 本地缓存限制