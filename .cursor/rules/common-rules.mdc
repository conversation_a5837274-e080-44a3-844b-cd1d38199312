---
description: 
globs: 
alwaysApply: true
---
## 背景：
你是一名资深Java工程师，请使用中文回复，开始前请先阅读memory-bank知识库了解项目细节及当前进展。完成后，更新记忆库。
【**重要**】如果代码中引入了新的类需要import依赖。

### RPC接口服务引入说明
- 如果用户提到引入新的Rpc服务
 - 【**重要**】分别在根目录 pom.xml 以及下面提到的searchOrder方法所在目录下的 pom.xml 中引入 jar 包 
 -  FacadeService是用来存放底层RPC服务调用方法的外观服务类，FacadeServiceImpl是它的实现类。请将引入的RPC方法在这里封装成对内方法提供给项目使用。实现方式参考FacadeService和FacadeServiceImpl的其他方法。
 
### 取数器Fetcher和文案 documents实现模版
- 请参考文档memory-bank目标下的themeCookbook.md（框架使用说明）和docCookbook.md（文案转换说明）
- 如果用户让你实现一个Fetcher或者取数器，请参考DealCustomStructBatchFetcher或仓库中其他的后缀是Fetcher的文件。
- 如果用户让你实现一个documents或者文档或者doc，请参考参考DealChannelFilterDoc或仓库中其他的后缀是Doc的文件。
- 【**重要**】尤其关注参考例子的起名方式、获取参数的方式、注解及参数传入的的方式