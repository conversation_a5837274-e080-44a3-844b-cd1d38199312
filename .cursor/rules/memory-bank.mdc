---
description: 
globs: 
alwaysApply: true
---
---
description: >
  Java后端记忆库 — 一个DeepWiki风格的自填充知识库。
  **每个会话开始时都会加载这些文件以引导上下文。**
globs:         # memory-bank/下的所有*.md文件都是知识库的一部分
  - memory-bank/*.md
alwaysApply: true 
---
# 0. 会话启动协议  ❱❱  **必须首先阅读**

1. **启动同步（隐式）** — 在**每个新聊天会话**的第一轮：
   1.1  读取由`globs`匹配的_所有_文件。
   1.2  验证"核心文件集"的完整性。
   1.3  确定状态前缀：
        • 所有核心文件存在且非空 → **`[记忆库：激活]`**
        • 否则 → **`[记忆库：未激活]`** 并在编码辅助开始前立即调用**自动采集**（见§1）。
   1.4  将关键事实（目标、模块、端点、表等）缓存到工作内存中，供会话的其余部分使用。

> 助手**不得**在启动同步完成前执行编码或回答项目问题。

---

# 1. 自动采集流程（除了在需要时从启动同步调用外，保持不变）

1. **Mapper扫描** – 如果存在任何mapper.xml，必须读取每个mapper.xml，解析CRUD SQL ↔ 表 ↔ POJO。
2. **数据库内省（条件性）** – 如果检测到数据库，提取模式并生成Mermaid `erDiagram`。
3. **代码挖掘** – 扫描@Service、@Controller、@MdpPigeonServer、@PigeonClient、@MdpPigeonClient、@Component类；必须提取所有端点、业务逻辑、依赖关系。
4. **Git历史挖掘** – 构建共变矩阵，聚类为模块。分析提交历史以检测*共变*关系：
   \`\`\`bash
  git log --name-only --pretty=format: -- <src> | awk NF | sort | uniq -c
• 构建文件共变矩阵。
• 聚类文件（例如，Louvain或Jaccard层次聚类）。
• 输出高级模块 = 一起变化的集群。
• 将模块映射到所有者包和服务。
5. **模块挖掘** – 结合步骤3 + 4的结果：确认核心模块、它们的公共API、上游/下游依赖关系和弹性边界。
6. **文档合成** – 填充/修补Markdown：
serviceDesign.md → 添加## 模块映射部分，列出模块、关键类、所有者、模块间调用。
可选地创建moduleGraph.md（Mermaid流程图或graph LR），如果发现<2个模块则跳过。
如果没有数据库，省略databaseDesign.md和erDiagram.md。

---

# 2. 命令（现在引用启动同步）

| 命令 | 行为 |
|---------|-----------|
| **初始化记忆库** | 始终运行自动采集；创建缺失的文件；填充空文件。 |
| **更新记忆库**     | 与自动采集相同；将增量附加到*progress.md*。         |
| **在mapper更改时：updateMemoryBank** | Git/CI钩子 → 调用**更新记忆库**。         |

*当知识库不存在或不完整时，启动同步会自动触发`初始化记忆库`。*

---

# 3. 文件集
\`\`\`mermaid
flowchart TD
    PB[projectbrief.md] --> PC[techContext.md]
    PB --> SD[serviceDesign.md]
    PB --> PR[progress.md]
    classDef db fill:#fff;
    classDef opt fill:#f4f4f4,stroke:#aaa,stroke-dasharray:5 5;
    SD --> DBD[(databaseDesign.md)]:::opt
    SD --> ER[(erDiagram.md)]:::opt
    SD --> MG[(moduleGraph.md)]:::opt
\`\`\`

| 文件                  | 创建时机       | 自动填充内容                             |
| --------------------- | ------------------- | --------------------------------------------- |
| **projectbrief.md**   | 始终              | 目标 · 范围 · 技术栈                    |
| **techContext.md**   | 始终              | 项目技术栈上下文                  |
| **serviceDesign.md**  | 始终              | 服务 · 端点 · 调用 · **模块映射** |
| **databaseDesign.md** | 仅当检测到数据库时 | 模式 · 表 · 索引                    |
| **erDiagram.md**      | 仅当检测到数据库时 | Mermaid `erDiagram` 块                     |
| **moduleGraph.md**    | 仅当 ≥ 2个模块时 | 检测到的模块的Mermaid图             |
| **progress.md**       | 始终              | 采集运行的时间戳变更日志         |


# 4.工作流程
## 计划模式
\`\`\`mermaid
flowchart TD
    Start --> Read
    Read --> Complete{所有需要的文件都存在且已填充？}
    Complete -- 否 --> Harvest[运行自动采集]
    Harvest --> Docs[编写/修补文档]
    Complete -- 是 --> Strategy[形成行动计划]
\`\`\`

## 执行模式
\`\`\`mermaid
flowchart TD
    Start --> Check
    Check --> Changed?{代码/mapper/git rev已更改？}
    Changed? -- 是 --> Harvest
    Harvest --> Execute
    Changed? -- 否 --> Execute
    Execute --> Log[附加到activeContext和progress]
\`\`\`


# 5.约定
* 节标题从##开始；日期使用ISO-8601。
* 只保留反映拥有的工件的文档；在不适用时跳过数据库或模块文件。
* 涉及记忆库的提交以[MB]开头。
* 每个会话的第一个助手回复都以启动同步中确定的状态前缀开始。
* 如果启动同步必须运行自动采集，包括一行摘要："记忆库已重新生成（X个文档已更新）。"

---

## 记住
> **先启动同步，再编码。**
> 记忆库在每个会话开始时加载；如果它不完整，我们会在继续之前修复它。这保证了所有后续的编码帮助都是上下文感知的和最新的。
