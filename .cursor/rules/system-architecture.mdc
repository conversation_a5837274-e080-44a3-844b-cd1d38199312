---
description: 
globs: 
alwaysApply: true
---
# System Architecture

## Documentation Structure
项目的详细说明组织在 @wiki-docs 目录中。
项目文档组织在 [memory-bank](mdc:memory-bank) 目录中，包含：


- 主题开发手册(themeCookbook.md)
- 主题开发文案-模型映射功能(docCookbook.md)
- 门面服务角色文档(facade_service_role.md)
- RPC服务文档(OrderSearchServiceRPC.md)
- 系统模式和技术上下文(systemPatterns.md, techContext.md)
- 项目进度和上下文(progress.md, productContext.md)

## Key System Components
- DealStockBatchFetcher: 处理团购库存的批量检索
- 交易文档系统: 管理产品属性和可用性
- 订单查询中心: 提供订单信息的RPC服务

## Design Patterns
- Batch 处理模式: 用于高效数据检索
- Document 处理模式: 用于产品属性管理
- Facade Services: 用于服务集成
- RPC Services: 用于服务间通信

