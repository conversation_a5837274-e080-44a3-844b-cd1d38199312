package com.sankuai.dealtheme.core.documents;

import com.sankuai.athena.theme.framework.annotations.Document;
import com.sankuai.athena.theme.framework.annotations.Param;
import com.sankuai.athena.theme.framework.documents.DocumentFields;
import com.sankuai.dealtheme.core.fetchers.model.DealModel;
import com.sankuai.dealtheme.core.fetchers.model.DealUserPurchaseInfoModel;
import com.sankuai.dealtheme.core.model.DealProductAttrDTO;
import lombok.extern.slf4j.Slf4j;

/**
 * 团单用户购买次数属性文案
 * 
 * <AUTHOR>
 * @date 2024/12/19
 */
@Slf4j
@Document(name = "dealUserPurchaseCountAttr", description = "团单用户购买次数属性")
public class DealUserPurchaseCountAttrDoc implements com.sankuai.athena.theme.framework.documents.Document<DealModel, DealProductAttrDTO> {

    @Override
    public DealProductAttrDTO transform(DealModel dealModel, 
                                       @Param(name = "userId", description = "用户ID") Long userId,
                                       @Param(name = "platform", description = "平台标识，1-点评，2-美团") Integer platform) {
        
        if (dealModel == null) {
            log.warn("DealUserPurchaseCountAttrDoc: dealModel为空");
            return null;
        }

        DealUserPurchaseInfoModel purchaseInfo = dealModel.getDealUserPurchaseInfo();
        if (purchaseInfo == null) {
            log.debug("DealUserPurchaseCountAttrDoc: 用户购买信息为空, dealId:{}", dealModel.getDealId());
            return createDefaultAttr(dealModel.getDealId(), 0);
        }

        Integer purchaseCount = purchaseInfo.getPurchaseCount();
        if (purchaseCount == null) {
            purchaseCount = 0;
        }

        return createDefaultAttr(dealModel.getDealId(), purchaseCount);
    }

    /**
     * 创建默认的属性DTO
     */
    private DealProductAttrDTO createDefaultAttr(Integer dealId, Integer purchaseCount) {
        DealProductAttrDTO attrDTO = new DealProductAttrDTO();
        attrDTO.setAttrKey("userPurchaseCount");
        attrDTO.setAttrValue(String.valueOf(purchaseCount));
        attrDTO.setAttrDesc("用户180天内购买次数");
        
        log.debug("DealUserPurchaseCountAttrDoc: 生成属性, dealId:{}, purchaseCount:{}", dealId, purchaseCount);
        
        return attrDTO;
    }

    @Override
    public String getFieldName() {
        return DocumentFields.ProductFieldNames.attrs;
    }
}
