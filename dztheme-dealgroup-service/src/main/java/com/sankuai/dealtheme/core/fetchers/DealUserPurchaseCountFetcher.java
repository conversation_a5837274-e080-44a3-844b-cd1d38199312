package com.sankuai.dealtheme.core.fetchers;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.athena.graphql.Execution;
import com.sankuai.athena.theme.framework.annotations.Fetcher;
import com.sankuai.athena.theme.framework.annotations.Param;
import com.sankuai.athena.theme.framework.fetchers.BatchFetcher;
import com.sankuai.dealtheme.core.fetchers.model.DealModel;
import com.sankuai.dealtheme.core.fetchers.model.DealUserPurchaseInfoModel;
import com.sankuai.dealtheme.core.nr.atom.FacadeService;
import com.sankuai.dealtheme.DealThemeConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 用户180天内在门店下的团单购买次数取数器
 * 
 * <AUTHOR>
 * @date 2024/12/19
 */
@Slf4j
@Fetcher(name = "dealUserPurchaseCount", description = "用户180天内在门店下的团单购买次数")
public class DealUserPurchaseCountFetcher extends BatchFetcher<DealModel, DealUserPurchaseInfoModel> {

    @Autowired
    private FacadeService facadeService;

    @Override
    public CompletableFuture<Map<DealModel, DealUserPurchaseInfoModel>> batchFetch(
            List<DealModel> dealModels,
            @Param(name = "userId", description = "用户ID") Long userId,
            @Param(name = "platform", description = "平台标识，1-点评，2-美团") Integer platform,
            Execution execution) {

        Map<DealModel, DealUserPurchaseInfoModel> result = Maps.newHashMap();
        
        if (userId == null || userId <= 0 || platform == null) {
            log.warn("DealUserPurchaseCountFetcher: 用户ID或平台参数无效, userId:{}, platform:{}", userId, platform);
            return CompletableFuture.completedFuture(result);
        }

        if (dealModels == null || dealModels.isEmpty()) {
            return CompletableFuture.completedFuture(result);
        }

        // 获取用户上下文信息进行验证
        Long sessionUserId = null;
        if (platform == 1) { // 点评平台
            sessionUserId = execution.getParameter(DealThemeConfig.DP_USER_ID);
        } else { // 美团平台
            sessionUserId = execution.getParameter(DealThemeConfig.MT_USER_ID);
        }

        if (sessionUserId == null || sessionUserId <= 0 || !sessionUserId.equals(userId)) {
            log.warn("DealUserPurchaseCountFetcher: 用户ID不匹配或无效, sessionUserId:{}, requestUserId:{}, platform:{}",
                    sessionUserId, userId, platform);
            return CompletableFuture.completedFuture(result);
        }

        // 按门店分组处理
        Map<Long, List<DealModel>> shopDealMap = Maps.newHashMap();
        for (DealModel dealModel : dealModels) {
            if (dealModel.getShopId() != null && dealModel.getShopId() > 0) {
                shopDealMap.computeIfAbsent(dealModel.getShopId(), k -> Lists.newArrayList()).add(dealModel);
            }
        }

        List<CompletableFuture<Void>> futures = Lists.newArrayList();
        
        for (Map.Entry<Long, List<DealModel>> entry : shopDealMap.entrySet()) {
            Long shopId = entry.getKey();
            List<DealModel> shopDeals = entry.getValue();
            
            CompletableFuture<Void> future = searchOrdersByShop(userId, platform, shopId, shopDeals, result);
            futures.add(future);
        }

        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .thenApply(v -> result);
    }

    /**
     * 根据门店查询用户订单并统计购买次数
     */
    private CompletableFuture<Void> searchOrdersByShop(Long userId, Integer platform, Long shopId, 
                                                       List<DealModel> shopDeals, 
                                                       Map<DealModel, DealUserPurchaseInfoModel> result) {
        
        return facadeService.searchOrder(userId, platform, shopId)
                .thenAccept(orderResult -> {
                    try {
                        if (orderResult == null || orderResult.get("data") == null) {
                            // 没有订单数据，设置购买次数为0
                            for (DealModel dealModel : shopDeals) {
                                DealUserPurchaseInfoModel purchaseInfo = new DealUserPurchaseInfoModel();
                                purchaseInfo.setDealId(dealModel.getDealId());
                                purchaseInfo.setPurchaseCount(0);
                                result.put(dealModel, purchaseInfo);
                            }
                            return;
                        }

                        @SuppressWarnings("unchecked")
                        List<Map<String, Object>> orders = (List<Map<String, Object>>) orderResult.get("data");
                        
                        // 统计每个团单的购买次数
                        Map<Integer, Integer> dealPurchaseCountMap = Maps.newHashMap();
                        for (Map<String, Object> order : orders) {
                            Object dealIdObj = order.get("dealId");
                            if (dealIdObj != null) {
                                Integer dealId = Integer.valueOf(dealIdObj.toString());
                                dealPurchaseCountMap.put(dealId, dealPurchaseCountMap.getOrDefault(dealId, 0) + 1);
                            }
                        }

                        // 为每个团单设置购买次数
                        for (DealModel dealModel : shopDeals) {
                            DealUserPurchaseInfoModel purchaseInfo = new DealUserPurchaseInfoModel();
                            purchaseInfo.setDealId(dealModel.getDealId());
                            Integer purchaseCount = dealPurchaseCountMap.getOrDefault(dealModel.getDealId(), 0);
                            purchaseInfo.setPurchaseCount(purchaseCount);
                            result.put(dealModel, purchaseInfo);
                        }

                    } catch (Exception e) {
                        log.error("DealUserPurchaseCountFetcher: 处理订单数据异常, userId:{}, shopId:{}, error:{}", 
                                userId, shopId, e.getMessage(), e);
                        // 异常情况下设置购买次数为0
                        for (DealModel dealModel : shopDeals) {
                            DealUserPurchaseInfoModel purchaseInfo = new DealUserPurchaseInfoModel();
                            purchaseInfo.setDealId(dealModel.getDealId());
                            purchaseInfo.setPurchaseCount(0);
                            result.put(dealModel, purchaseInfo);
                        }
                    }
                })
                .exceptionally(ex -> {
                    log.error("DealUserPurchaseCountFetcher: 查询订单异常, userId:{}, shopId:{}, error:{}", 
                            userId, shopId, ex.getMessage(), ex);
                    // 异常情况下设置购买次数为0
                    for (DealModel dealModel : shopDeals) {
                        DealUserPurchaseInfoModel purchaseInfo = new DealUserPurchaseInfoModel();
                        purchaseInfo.setDealId(dealModel.getDealId());
                        purchaseInfo.setPurchaseCount(0);
                        result.put(dealModel, purchaseInfo);
                    }
                    return null;
                });
    }
}
