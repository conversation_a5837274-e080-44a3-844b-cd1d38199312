package com.sankuai.dealtheme.core.fetchers.model;


import com.sankuai.athena.theme.framework.annotations.Field;
import com.sankuai.athena.theme.framework.annotations.Type;
import com.sankuai.dealtheme.core.fetchers.model.beauty.BeautyCheapHotModel;
import com.sankuai.dealtheme.core.fetchers.model.beauty.BeautyDealTagModel;
import com.sankuai.dealtheme.core.fetchers.model.beauty.DealDetailTextModel;
import com.sankuai.dealtheme.core.fetchers.model.bpDealGroup.BpDealGroupModel;
import com.sankuai.dealtheme.core.fetchers.model.consumer.ConsumerPlanInfoModel;
import com.sankuai.dealtheme.core.fetchers.model.filter.ChannelFilterModel;
import com.sankuai.dealtheme.core.fetchers.model.home.ApplianceMiniprogramUrlModel;
import com.sankuai.dealtheme.core.fetchers.model.home.HomeShopModel;
import com.sankuai.dealtheme.core.fetchers.model.leads.LeadsInfoModel;
import com.sankuai.dealtheme.core.fetchers.model.live.LiveAnchorModel;
import com.sankuai.dealtheme.core.fetchers.model.live.LiveDetailModel;
import com.sankuai.dealtheme.core.fetchers.model.mall.DealRelatedMallModel;
import com.sankuai.dealtheme.core.fetchers.model.material.MaterialModel;
import com.sankuai.dealtheme.core.fetchers.model.other.shop.OtherShopModel;
import com.sankuai.dealtheme.core.fetchers.model.play.PlayActivityModel;
import com.sankuai.dealtheme.core.fetchers.model.product.DealTemplateDetailModel;
import com.sankuai.dealtheme.core.fetchers.model.product.DealThirdPartyModel;
import com.sankuai.dealtheme.core.fetchers.model.product.baby.BabyHotSaleDealTagModel;
import com.sankuai.dealtheme.core.fetchers.model.product.baby.DealBindExhibitModel;
import com.sankuai.dealtheme.core.fetchers.model.promo.DealPriceDisplayModel;
import com.sankuai.dealtheme.core.fetchers.model.rank.RankDetailModel;
import com.sankuai.dealtheme.core.fetchers.model.shop.DealApplyCityModel;
import com.sankuai.dealtheme.core.fetchers.model.shop.DealApplyShopModel;
import com.sankuai.dealtheme.core.fetchers.model.shop.DealShopInfoModel;
import com.sankuai.dealtheme.core.fetchers.model.sku.DealSkuInfoModel;
import com.sankuai.dealtheme.core.fetchers.model.stock.LatestStockModel;
import com.sankuai.dealtheme.core.fetchers.model.stock.LifeWashLatestStockModel;
import com.sankuai.dealtheme.core.fetchers.model.subjectSale.SubjectSaleModel;
import com.sankuai.dealtheme.core.fetchers.model.swan.SwanQueryModel;
import com.sankuai.dealtheme.core.fetchers.model.tech.PromoCodeProductJumpUrlModel;
import com.sankuai.dealtheme.core.fetchers.model.tech.TechDetailModel;
import com.sankuai.dealtheme.core.fetchers.model.vaccine.ResvNumModel;
import com.sankuai.dealtheme.core.fetchers.model.vaccine.VaccineDealTagModel;
import lombok.Data;

import java.util.List;

/**
 * created by zhangzhiyuan in 2020/6/12
 * 数据聚合模型，对领域建模无要求
 */
@Data
@Type(name = "Deal", description = "团单")
public class DealModel {

    @Field(name = "dealId", description = "团单ID")
    private int dealId;

    @Field(name = "platform", description = "平台")
    private int platform;

    @Field(name = "dealBase", description = "团单基础信息")
    private DealBaseModel dealBase;

    @Field(name = "dealShopSale", description = "团单商户销量信息")
    private DealShopSaleModel dealShopSale;

    @Field(name = "dealShopSales", description = "团单商户销量信息列表")
    private List<DealShopSaleModel> dealShopSales;

    @Field(name = "dealGatherSales", description = "团单的集合销量销量")
    private List<DealGatherSaleModel> dealGatherSales;

    @Field(name = "dealSale", description = "团单在所有适用商户下的总销量")
    private DealSaleModel dealSale;

    @Field(name = "dealAvailableDate", description = "团单可用时间段")
    private DealAvailableDateModel dealAvailableDate;

    @Field(name = "dealAvailableDisableDate", description = "团单可用和不可用时间")
    private DealAvailableDisableDateModel dealAvailableDisableDate;

    @Field(name = "dealStruct", description = "团单结构化信息")
    private DealStructModel dealStruct;

    @Field(name = "dealRelateProjects", description = "团单关联的服务项目（推荐菜）信息")
    private DealRelateProjectsModel dealRelateProjects;

    @Field(name = "dealPin", description = "拼团信息")
    private DealPinModel dealPin;

    @Field(name = "dealReturnCoupon", description = "团单消费返券")
    private DealReturnCouponModel dealReturnCoupon;

    @Field(name = "dealRecommend", description = "商家推荐")
    private DealRecommendModel dealRecommend;

    @Field(name = "toothRecommendDeal", description = "商家推荐团单id")
    private Long toothRecommendDeal;

    @Field(name = "dealAttribute", description = "团购属性信息")
    private DealAttributeModel dealAttribute;

    @Field(name = "dealRelatedExhibitsModel", description = "团单关联案例信息")
    private DealRelatedExhibitsModel dealRelatedExhibitsModel;

    @Field(name = "dealDetailText", description = "团单详情页内容")
    private DealDetailTextModel dealDetailText;

    @Field(name = "dealGroupAttribute", description = "团单详情属性内容")
    private DealGroupAttributeModel dealGroupAttribute;

    @Field(name = "dealNearestShop", description = "团单最近商户信息")
    private DealNearestShopModel dealNearestShop;

    @Field(name = "dealShopTheme", description = "团单关联的商户主题信息")
    private ShopThemeModel dealShopTheme;

    /**
     * 使用 - dealApplyShop
     */
    @Field(name = "applyShops", description = "团单适用商户信息")
    @Deprecated
    private List<ShopThemeModel> applyShops;

    @Field(name = "dealApplyShop", description = "团单适用商户信息")
    private List<DealApplyShopModel> dealApplyShop;

    @Field(name = "dealCityApplyShop", description = "团单适用商户信息-根据城市做过滤，针对大客场景")
    private DealCityApplyShopModel dealCityApplyShop;

    @Field(name = "dealApplyCity", description = "团单适用城市信息")
    private DealApplyCityModel dealApplyCity;

    @Field(name = "dealCategory", description = "团单类目信息")
    private DealCategoryModel dealCategory;

    @Field(name = "dealShelfProduct", description = "团单教育货架信息")
    private DealShelfProductModel dealShelfProduct;

    @Field(name = "dealTimeCard", description = "团单次卡信息")
    private DealTimeCardModel dealTimeCard;

    @Field(name = "dealSaleOnTargetShop", description = "当前商户销量信息")
    private DealShopSaleModel dealSaleOnTargetShop;

    @Field(name = "dealActivity", description = "团单彩虹活动信息")
    private DealActivityModel dealActivity;

    @Field(name = "dealCard", description = "卡信息")
    private DealCardModel dealCard;

    @Field(name = "dealCustomStruct", description = "业务定制的团单结构化数据")
    private DealCustomStructModel dealCustomStruct;

    @Field(name = "dealProductActivity", description = "团单参与的活动信息")
    private DealProductActivityModel dealProductActivity;

    @Field(name = "dealProductPriceActivity", description = "团单参与的价格相关的活动信息，比如价格优惠前的活动信息等")
    private DealProductActivityModel dealProductPriceActivity;

    @Field(name = "beautyDealTags", description = "丽人团单的标签信息")
    private BeautyDealTagModel beautyDealTags;

    @Field(name = "beautySpecialPromotionDealTag", description = "丽人特价团购标签信息")
    private String beautySpecialPromotionDealTag;

    @Field(name = "beautyCheapHot", description = "丽人低价爆款信息")
    private BeautyCheapHotModel beautyCheapHot;

    @Field(name = "dealLatestSale", description = "团单最近交易信息")
    private DealProductSaleModel dealLatestSale;

    @Field(name = "dealShopLatestSale", description = "团单在商户维度的最近交易信息")
    private DealProductSaleModel dealShopLatestSale;

    @Field(name = "dealBookInfo", description = "齿科预订信息")
    private DealBookModel dealBookInfo;

    @Field(name = "dealFloorRelation", description = "团单楼层关系")
    private DealProductActivityModel dealFloorRelation;

    @Field(name = "dealVoucherInfo", description = "团单消费凭证")
    private DealVoucherInfoModel dealVoucherInfo;

    @Field(name = "dealOperatorActivity", description = "团单运营活动")
    private DealProductActivityModel dealOperatorActivity;

    @Field(name = "dealActivityStatus", description = "团单彩虹活动状态")
    private DealActivityStatusModel dealActivityStatus;

    @Field(name = "vaccineDealTag", description = "疫苗团单标签信息")
    private VaccineDealTagModel vaccineDealTag;

    @Field(name = "dealTemplateDetail", description = "团购模板化详情")
    private DealTemplateDetailModel dealTemplateDetail;

    @Field(name = "babyHotSaleDealTag", description = "亲子爆款团购标签")
    private BabyHotSaleDealTagModel babyHotSaleDealTag;

    @Field(name = "dealProductScaleAccumSale", description = "团购商品维度累计销量")
    private DealAccumSaleModel dealProductScaleAccumSale;

    @Field(name = "dealProductScaleCycleSale", description = "团单原始销量，因为某些品类下团单会做销量聚合")
    private DealAccumSaleModel dealProductScaleCycleSale;

    @Field(name = "dealThirdPartyInfo", description = "团购第三方信息")
    private DealThirdPartyModel dealThirdPartyInfo;

    @Field(name = "dealBindExhibit", description = "摄影团购绑定的主题")
    private DealBindExhibitModel dealBindExhibit;

    @Field(name = "dealBindExhibitCount", description = "团购绑定展示样式的数量")
    private int dealBindExhibitCount;

    @Field(name = "dealPriceDisplay", description = "团单价格展示信息")
    private DealPriceDisplayModel dealPriceDisplay;

    @Field(name = "dealExtraChargeList", description = "团单额外费用")
    private List<DealExtraChargeModel> dealExtraChargeList;
    /**
     * {@link com.sankuai.dealtheme.core.fetchers.attr.ValidProductTagIdBatchFetcher}
     */
    @Field(name = "validProductTagIds", description = "有效的商品标签Id")
    private List<Long> validProductTagIds;

    @Field(name = "stock", description = "团单库存")
    private DealStockModel stock;

    @Field(name = "dealSecKillActivity", description = "团单参与的秒杀活动信息")
    private DealProductActivityModel dealSecKillActivity;

    @Field(name = "review", description = "团单评价信息")
    private DealReviewModel review;

    @Field(name = "dealDisplayControlCheckStatus", description = "团单展示信息合规校验结果")
    private int dealDisplayControlCheckStatus;

    @Field(name = "dealAvailableDetailDateModels", description = "使用时间详细描述包含多种类型")
    private List<DealAvailableDetailDateModel> dealAvailableDetailDateModels;

    @Field(name = "dealCustomer", description = "团单对应客户信息")
    private DealCustomerModel dealCustomer;

    /**
     * {@link com.sankuai.dealtheme.core.fetchers.product.DealGroupCustomerPlatformBatchFetcher}
     */
    @Field(name = "dealCustomerPlatform", description = "团单对应平台客户信息")
    private DealCustomerModel dealCustomerPlatform;

    @Field(name = "otherShop", description = "非标商户维度信息")
    private OtherShopModel otherShop;

    @Deprecated
    @Field(name = "dealRelateContent", description = "团单关联内容信息")
    private DealRelateContentModel dealRelateContent;

    @Field(name = "dealMultiRelateContent", description = "团单关联内容信息列表")
    private List<DealRelateContentModel> dealMultiRelateContent;

    @Field(name = "hasBabyOutdoorTheme", description = "是否有关联外景主题")
    private boolean hasBabyOutdoorTheme;

    @Field(name = "productValidTags", description = "团单有效标签列表，标签来源于商品打标系统")
    private List<DealTagModel> productValidTags;

    @Field(name = "tags", description = "团单标签列表")
    private List<DealTagModel> tags;

    @Field(name = "recentlyViewed", description = "团单最近被浏览信息")
    private String recentlyViewed;

    @Field(name = "recommendPicModel", description = "推荐图片信息")
    private RecommendPicModel recommendPicModel;

    @Field(name = "recommendInfo", description = "推荐信息")
    private RecommendInfoModel recommendInfo;

    @Field(name = "orderUsers", description = "商品关联的订单用户信息列表")
    private List<OrderUserModel> orderUsers;

    @Field(name = "orderUrl", description = "团购的提单页跳转链接")
    private String orderUrl;

    @Field(name = "babySecKillActivity", description = "亲子秒杀活动")
    private DealProductBabySecKillActivityModel babySecKillActivity;

    @Field(name = "hotSaleDeal", description = "爆款团购")
    private HotSaleDealModel hotSaleDeal;

    @Field(name = "bpDealGroup", description = "商品BP团单信息，适配商品侧团单新接口模型")
    private BpDealGroupModel bpDealGroup;

    @Field(name = "dealRecommendResultModel", description = "团单推荐结果信息")
    private DealRecommendResultModel dealRecommendResultModel;

    @Field(name = "dealRecommendResultList", description = "多个类型的团单推荐结果信息")
    private List<DealRecommendResultModel> dealRecommendResultList;

    @Field(name = "memberExclusive", description = "是否是会员专属商品")
    private Boolean memberExclusive;

    @Field(name = "dealSubTitle", description = "团单副标题")
    private List<String> dealSubTitle;

    @Field(name = "dealShopInfo", description = "门店信息")
    private DealShopInfoModel dealShopInfo;

    @Field(name = "hookProductGroup", description = "是否是钩子单")
    private boolean hookProductGroup;

    @Field(name = "techDetails", description = "商品关联手艺人信息")
    private List<TechDetailModel> techDetails;

    @Field(name = "recommendTitle", description = "推荐算法生成的标题")
    private String recommendTitle;

    @Field(name = "dealRelatedMall", description = "团单关联商场信息")
    private DealRelatedMallModel dealRelatedMall;

    @Field(name = "dealSkuInfo", description = "团单Sku信息")
    private DealSkuInfoModel dealSkuInfo;

    @Field(name = "medicalBookingTags", description = "医疗体检标签")
    private MedicalBookingTagsModel medicalBookingTags;

    @Field(name = "channelMedicalBookingTags", description = "频道页医疗体检标签")
    private MedicalBookingTagsModel channelMedicalBookingTags;

    @Field(name = "resvNum" , description = "预约数量")
    private ResvNumModel resvNum;

    @Field(name = "leadsSales" , description = "留资数量")
    private LeadsSalesModel leadsSales;

    @Field(name = "leadsInfo", description = "留资信息")
    private LeadsInfoModel leadsInfo;

    @Field(name = "resvStatus" , description = "预约状态")
    private Boolean resvStatus;

    @Field(name = "rankDetail", description = "团单榜单信息")
    private RankDetailModel rankDetail;

    @Field(name = "priceProtectionTag", description = "价保标签信息")
    private PriceProtectionTagModel priceProtectionTag;

    @Field(name =  "guaranteeObjectQuery",description = "保价服务返回信息")
    private GuaranteeObjectQueryModel guaranteeObjectQueryModel;
    @Field(name = "liveAnchor", description = "主播信息")
    private LiveAnchorModel liveAnchor;

    @Field(name = "liveDetail", description = "直播消息推送入参")
    private LiveDetailModel liveDetail;

    @Field(name = "swanQueryModel", description = "swan查询信息聚合")
    private SwanQueryModel swanQueryModel;

    @Field(name = "dzShopCarOrderUrl", description = "到综购物车提单页链接")
    private String dzShopCarOrderUrl;

    @Field(name = "dealHomeShop", description = "团单关联的家居商户主题信息")
    private HomeShopModel dealHomeShop;

    @Field(name = "channelFilter", description = "渠道过滤")
    private ChannelFilterModel channelFilter;

    @Field(name = "merchantMemberDeal", description = "商户会员价团单信息")
    private MerchantMemberDealModel merchantMemberDeal;

    @Field(name = "courseMajorTeacherName", description = "课程主讲师名（特殊逻辑，在线教育专用）")
    private String courseMajorTeacherName;

    @Field(name = "hairBookingTagsModel", description = "养发可预约标签")
    private HairBookingTagsModel hairBookingTagsModel;

    @Field(name = "dealRelatedCaseModel", description = "团单关联案例信息")
    private DealRelatedExhibitsModel dealRelatedCaseModel;

    @Field(name = "consumerPlanInfoModel", description = "团单到店信息,如到店礼")
    private ConsumerPlanInfoModel consumerPlanInfoModel;

    // 丽人穿戴甲
    @Field(name = "materialModel", description = "团单素材信息")
    private List<MaterialModel> materialModel;

    @Field(name = "reservationStock", description = "团单预约库存信息")
    private LatestStockModel reservationStock;

    @Field(name = "eduTrialVideos", description = "团单视频信息")
    private List<DealVideoModel> eduTrialVideos;

    @Field(name = "playActivityModelList", description = "玩法活动模型信息")
    private List<PlayActivityModel> playActivityModelList;

    @Field(name ="activityCornerLabel" , description = "活动头图角标")
    private ActivityCornerLabelModel activityCornerLabel;

    @Field(name = "additionalProjectList", description = "团单加项列表")
    private List<DealAdditionalProjectModel> additionalProjectList;

    @Field(name = "dealInsurance", description = "团单关联保险信息")
    private DealInsuranceModel dealInsurance;

    @Field(name = "medicalSafeTreatTagModel", description = "医疗安心医标签")
    private MedicalSafeTreatTagModel medicalSafeTreatTagModel;

    @Field(name = "laundrySupportDelivery", description = "洗涤支持配送信息")
    private LaundrySupportDeliveryModel laundrySupportDelivery;

    @Field(name = "laundryWhiteCustomerIdList", description = "洗涤白名单客户ID列表")
    private List<Long> laundryWhiteCustomerIdList;

    @Field(name = "promoCodeProductJumpUrl", description = "手艺人跳链信息")
    private PromoCodeProductJumpUrlModel promoCodeProductJumpUrl;

    @Field(name = "postpartumHouseInfo", description = "月子中心房型信息")
    private PostpartumHouseInfoModel postpartumHouseInfo;

    @Field(name = "subjectSaleModel", description = "特殊主体的销量查询")
    private SubjectSaleModel subjectSaleModel;

    @Field(name = "nursingCenterRoomSecretHandPrice", description = "月子中心房型优惠团单打标价")
    private String nursingCenterRoomSecretHandPrice;

    @Field(name = "postpartumVrUrl", description = "月子中心房型团单vrUrl")
    private String postpartumVrUrl;

    @Field(name = "lifeWashReservationSale", description = "预订销量信息")
    private LifeWahReserveSaleInfoModel lifeWashReservationSale;

    @Field(name = "lifeReservationStock", description = "家政保洁预订团单预约库存信息")
    private LifeWashLatestStockModel lifeReservationStock;

    @Field(name = "dealUserPurchaseInfo", description = "用户团单购买信息")
    private DealUserPurchaseInfoModel dealUserPurchaseInfo;

    @Field(name = "applianceMiniprogramUrlModel", description = "家电货架小程序跳链")
    private ApplianceMiniprogramUrlModel applianceMiniprogramUrlModel;

    @Field(name = "dealSkuModels", description = "SKU 信息")
    private List<DealSkuModel> dealSkuModels;
}
