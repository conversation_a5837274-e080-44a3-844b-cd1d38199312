package com.sankuai.dealtheme.core.fetchers.model;

import com.sankuai.athena.theme.framework.annotations.Field;
import com.sankuai.athena.theme.framework.annotations.Type;
import lombok.Data;

import java.io.Serializable;

/**
 * 用户团单购买信息模型
 * 
 * <AUTHOR>
 * @date 2024/12/19
 */
@Data
@Type(name = "DealUserPurchaseInfo", description = "用户团单购买信息")
public class DealUserPurchaseInfoModel implements Serializable {

    @Field(name = "dealId", description = "团单ID")
    private Integer dealId;

    @Field(name = "purchaseCount", description = "购买次数")
    private Integer purchaseCount;
}
