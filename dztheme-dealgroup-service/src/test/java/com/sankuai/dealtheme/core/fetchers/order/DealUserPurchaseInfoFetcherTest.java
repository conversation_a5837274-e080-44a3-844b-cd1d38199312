package com.sankuai.dealtheme.core.fetchers.order;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.athena.client.container.AthenaBeanFactory;
import com.sankuai.athena.graphql.Execution;
import com.sankuai.athena.graphql.ExecutionContext;
import com.sankuai.athena.graphql.FetchingContext;
import com.sankuai.dealtheme.DealThemeConfig;
import com.sankuai.dealtheme.core.fetchers.model.DealModel;
import com.sankuai.dealtheme.core.fetchers.model.DealUserPurchaseInfoModel;
import com.sankuai.dealtheme.core.nr.atom.FacadeService;
import com.sankuai.general.order.querycenter.api.response.OrderSearchResponse;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class DealUserPurchaseInfoFetcherTest {
    @InjectMocks
    private DealUserPurchaseInfoFetcher fetcher;
    @Mock
    private FacadeService facadeService;

    @Mock
    private Execution execution;

    @Mock
    private ExecutionContext executionContext;

    @Mock
    private FetchingContext fetchingContext;
    private Map<Integer,FetchingContext> keyFetchContexts;
    private MockedStatic<AthenaBeanFactory> anthenaBeanFactoryMockedStatic;


    @Before
    public void setUp() {
        keyFetchContexts = new HashMap<>();
        keyFetchContexts.put(123, fetchingContext);
        when(fetchingContext.getExecutionContext()).thenReturn(executionContext);
        when(executionContext.getExecution()).thenReturn(execution);
        when(fetchingContext.getExecutionContext()).thenReturn(executionContext);
        when(executionContext.getExecution()).thenReturn(execution);
        anthenaBeanFactoryMockedStatic = Mockito.mockStatic(AthenaBeanFactory.class);
        anthenaBeanFactoryMockedStatic.when(() -> AthenaBeanFactory.getBean(FacadeService.class)).thenReturn(facadeService);
        ReflectionTestUtils.setField(fetcher, "MAX_QUERY_SIZE", 100);
    }

    @After
    public void after() {
        anthenaBeanFactoryMockedStatic.close();
    }

    @Test
    public void testBatchKey() {
        // 准备测试数据
        DealModel dealModel = new DealModel();
        dealModel.setDealId(123);
        when(fetchingContext.getSource()).thenReturn(dealModel);

        // 执行测试
        Integer result = fetcher.batchKey(fetchingContext);

        // 验证结果
        assertEquals(Integer.valueOf(123), result);
    }

    @Test
    public void testBatchGet_NormalCase() {
            // Mock Execution 参数
            when(execution.getParameter(DealThemeConfig.PLATFORM)).thenReturn(2);
            when(execution.getParameter(DealThemeConfig.USER_ID)).thenReturn(1001L);
            when(execution.getParameter(DealThemeConfig.DP_SHOP_ID_FOR_LONG)).thenReturn(2001L);
            when(execution.getParameter(DealThemeConfig.MT_SHOP_ID_FOR_LONG)).thenReturn(3001L);

            // Mock 查询结果
            Map<String, String> orderData = new HashMap<>();
            orderData.put("spugid", "123");
            OrderSearchResponse response = new OrderSearchResponse();
            response.setData(Lists.newArrayList(orderData));
            response.setTotalHit(1);

            // Mock FacadeService
            when(facadeService.searchOrder(any(), any())).thenReturn(CompletableFuture.completedFuture(response))
                    .thenReturn(CompletableFuture.completedFuture(null));// 模拟第二次查询点评数据为空
            when(facadeService.getDpRealUserIdByMtUserId(any())).thenReturn(CompletableFuture.completedFuture(2001L));

            // Mock dp2mtDealIdMap
            Map<Integer, Integer> dp2mtDealIdMap = Maps.newHashMap();
            dp2mtDealIdMap.put(123, 456);
            when(executionContext.getAttach(any())).thenReturn(CompletableFuture.completedFuture(dp2mtDealIdMap));

            // 执行测试
            Map<Integer, DealUserPurchaseInfoModel> result = fetcher.batchGet(keyFetchContexts).join();


            // 验证结果
            assertNotNull(result);
            assertEquals(1, result.size());
            DealUserPurchaseInfoModel model = result.get(456);
            assertNotNull(model);
            assertEquals(1, model.getDealPurchaseNums());
    }

    @Test
    public void testBatchGet_EmptyResponse() {
            // Mock Execution 参数
            when(execution.getParameter(DealThemeConfig.PLATFORM)).thenReturn(2);
            when(execution.getParameter(DealThemeConfig.USER_ID)).thenReturn(1001L);
            when(execution.getParameter(DealThemeConfig.DP_SHOP_ID_FOR_LONG)).thenReturn(2001L);
            when(execution.getParameter(DealThemeConfig.MT_SHOP_ID_FOR_LONG)).thenReturn(3001L);

            // Mock 空结果
            OrderSearchResponse response = new OrderSearchResponse();
            response.setData(Lists.newArrayList());
            response.setTotalHit(0);

            // Mock FacadeService
            when(facadeService.searchOrder(any(), any())).thenReturn(CompletableFuture.completedFuture(response));
            when(facadeService.getDpRealUserIdByMtUserId(any())).thenReturn(CompletableFuture.completedFuture(2001L));

            // Mock dp2mtDealIdMap
            Map<Integer, Integer> dp2mtDealIdMap = Maps.newHashMap();
            dp2mtDealIdMap.put(123, 456);
            when(executionContext.getAttach(any())).thenReturn(CompletableFuture.completedFuture(dp2mtDealIdMap));
            // 执行测试
            Map<Integer, DealUserPurchaseInfoModel> result = fetcher.batchGet(keyFetchContexts).join();

            // 验证结果
            assertNotNull(result);
            assertEquals(1, result.size());
            DealUserPurchaseInfoModel model = result.get(456);
            assertNotNull(model);
            assertEquals(0, model.getDealPurchaseNums());
    }

    @Test
    public void testBatchGet_NullContext() {
        // 执行测试
        Map<Integer, DealUserPurchaseInfoModel> result = fetcher.batchGet(null).join();

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.size());
    }
}