# 1.背景

主题引擎的执行结果是包含一系列文案的一个结果列表，在实践过程中，我们发现随着文案的增加，这个结果中的内容将愈来愈多，最终形成“文案爆炸”的现象，代码难以维护。

代码块Java
```java
public List<Record> query(DealProductRequest request) {
    PlanRequest planRequest = buildPlanRequest(request.getPlanId(), request.getExtParams());
    CompletableFuture<PlanResponse> execute = planEngine.execute(planRequest);
    PlanResponse join = execute.join();
    List<Record> data = join.getData();//该Record内为一个key-value内容，key的数量不可控
    return data;
}
```

我们认为主题结果应该是被抽象出来的一个模型，诸多文案应该是这个模型某字段的一种实现。

主题框架现增加自动转换生成主题结果模型的能力，可作为一种实践供开发同学参考

代码块Java
```java
public DealProductResult query(DealProductRequest request) {
    PlanRequest planRequest = buildPlanRequest(request.getPlanId(), request.getExtParams());
    CompletableFuture<PlanResponse> execute = planEngine.executeAndGetModel(planRequest);
    PlanResponse join = execute.join();
    List<DealProductDTO> models = join.getModels();//自动转换结果
    return models;
}
```

# 2.功能使用方法

## 2.1 引入依赖

服务端依赖：

代码块XML
```xml
<dependency>
    <groupId>com.sankuai</groupId>
    <artifactId>athena-framework-themeclient</artifactId>
    <version>0.1.11</version>
</dependency>
```

接口依赖（Jar包依赖）

代码块XML

```xml
<dependency>
    <groupId>com.sankuai</groupId>
    <artifactId>athena-framework-themeapi</artifactId>
    <version>0.0.2</version>
</dependency>
```

## 2.2 声明模型

一般我们的Rpc服务，都提供给外部一个jar包，用于提供外部调用的接口，我们将在该包中声明模型。我们以团单主题为例：

【必须】被使用的类上标注@Model注解，声明description字段，根节点类（第一个节点类）需要声明themeId，themeId与主题ID一致。

【必须】被使用的类上被使用的字段标注@Field注解，声明description字段。

团单主题的模型是DealProductDTO，需要标注Model注解，填写description字段，该类为根节点类型，所以还需要标注themeId。

DealProductDTO下的productId、categoryId、sale字段都会被文案填充，所以都需要标注Field注解。coupon字段没有使用，所以可以不标注Field注解。

sale字段的DealProductSaleDTO下saleTag字段会被映射成文案，所以DealProductSaleDTO类需要标注Model注解，saleTag字段需要标注Field注解，声明description，但是DealProductSaleDTO不是根节点类，所以不需要声明themeId。

代码块Java
```java
@Model(description = "团单主题模型", themeId = 100)
public class DealProductDTO implements Serializable {
    @Field(description = "商品ID， 一般是spuId")
    private int productId;
    @Field(description = "商品类目")
    private int categoryId;
    @Field(description = "商品销量信息")
    private DealProductSaleDTO sale;
    private DealProductCouponDTO coupon;
}

@Model(description = "商品销量信息")
public class DealProductSaleDTO implements Serializable {
    private int sale;
    @Field(description = "商品销量标签")
    private String saleTag;
}
```

另外该包下的pom文件中，需要指定maven插件。

代码块XML
```xml
<build>
    <plugins>
    <plugin>
        <groupId>com.sankuai</groupId>
        <artifactId>athena-framework-themeplugin</artifactId>
        <version>0.0.1</version>
        <executions>
        <execution>
        <!-- 配置compile执行 -->
        <phase>compile</phase>
        <goals>
            <!-- 配置执行目标 -->
            <goal>reporter</goal>
        </goals>
        <configuration>
            <!-- 根节点类名 -->
            <className>com.sankuai.dztheme.deal.dto.DealProductDTO</className>
        </configuration>
        </execution>
        </executions>
    </plugin>
</plugins>
</build>
```

## 2.3 关联字段

文案开发中，需要在文案的注解上填写modelField的对应字段并与上文的模型字段对应起来。

以团单ID文案为例：modelField需要填写为productId，并且类型需保持一致。

代码块Java
```java
@Doc(name = "dealId"
needFields = {"dealId"},
modelField = "productId")
public class DealIdDocument implements Document<Integer> {
    ​
    @Override
    public Integer execute(DocContext docContext) {
        Record record = docContext.getSubject();
        Integer dealId = record.getField("dealId");
        return dealId;
    }
}
```
另外，文案可以在下级节点上进行挂载，就是文案的多级遍历，多级映射功能。

比如DealProductSaleDTO下的sale节点也可以映射为文案，文案的类型和上级节点也需要和结果模型驳斥一致：

代码块Java
```java
@Doc(name = "saleSample",
needFields = {"dealId"},
location = "sale"//代表挂载的节点是schema中的sale字段，当声明这个值时，record内不再是根节点内容，而是根节点下的sale节点内容
modelField = "sale.saleTag")//代表映射的是模型中sale字段下的saleTag字段
public class SaleTagDocument implements Document<String> {
    ​
    @Override
    public String execute(DocContext docContext) {
        return saleTag;
    }
}
```

## 2.4 创建Plan

于平台创建Plan时，与原来的方式一致，新增了通过模型字段去选择文案的能力。

用户可以根据主题模型中的字段去选择其对应的文案，非List的类型为单选，List类型的为多选，被选择的文案将依次填充到List列表里。

## 2.5 调用接口

引擎新增接口获取结果。

代码块Java
```java
//构建请求
PlanRequest planRequest = buildPlanRequest(request.getPlanId(), request.getExtParams());
//引擎执行，使用executeAndGetModel方法
CompletableFuture<DealProductResult> dealProductResultFuture = planEngine.executeAndGetModel(planRequest).thenApply(planResponse -> {
    DealProductResult dealProductResult = new DealProductResult();
    //planResponse新增getModels方法，该方法将返回List<T>，在不同的主题下将返回不同的结果。用户可以直接转为主题模型，比如该代码中的团单模型DealProductDTO
    List<DealProductDTO> models = planResponse.getModels();
    dealProductResult.setDeals(models);
    dealProductResult.setMessage(JsonCodec.encode(planResponse.getErrors()));
    logPlanResponse(request.getPlanId(), planResponse);
    return dealProductResult;
});
//推荐返回时为限时返回，防止阻塞带来的隐患
return dealProductResultFuture.get(timeout);
```