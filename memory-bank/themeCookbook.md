## 主题架构
雅典娜提供了一套主题开发框架，基于主题开发框架，业务开发同学只需要编写业务逻辑即可，其中业务逻辑包括数据源的接入以及展示逻辑的编写，在这个基础之上，主题自动具备下图所示的能力，包括：
  文案的按需查询、主题场景方案及排序策略的配置
  按需使用的稳定性保障能力以及组件的运营能力

## 名词解释
+ 主题：一个信息展示的业务主体，如团购信息、商户信息
+ Schema：即 GraphQL 的 Schema ，可简单理解为系统内部的数据聚合模型，也可称为 Model
+ 取数器：调用内/外部数据源，将元数据加工映射到 Schema 中。因实现方式为实现 Fetcher 接口，所以也称为 Fetcher
+ 文案：使用数据层聚合数据加工出来的展示信息，并映射到对外展示模型上。因实现方式为实现 Document 接口，也称为 Document / Doc
+ 方案/Plan：一个展示场景下所需要的文案组织起来即为一个方案

## 如何搭建主题
### 引入开发框架
通过如下pom dependency声明引入主题开发框架客户端：

```xml
<dependency>
   <groupId>com.sankuai</groupId>
   <artifactId>athena-framework-themeclient</artifactId>
   <version>0.2.25</version>
</dependency>
```

### 主题开发
一个主题的搭建需要经过以下几个步骤：1）主题配置声明 2）取数层开发；3）文案层开发；4）主题接口开发；

步骤介绍：
1. 主题配置声明 主题配置编写 主题引擎是随spring启动的，所以需要在xml配置中声明包路径，并在该包下创建主题配置类。

2. 数据层开发 数据模型schema设计与实现 这里的schema指的GraphQL中的schema，此处用于描述数据的组织方式与数据之间的关系
3. schema取数器实现 实现标准Fetcher接口，实现schema中描述的数据的取数逻辑
4. 文案层开发 文案声明与生产逻辑编写 实现Document接口，为当前主题创建一个文案，该文案基于上下文参数和数据层读取的数据生产
5. 主题接口定义 定义和实现主题对外的接口 为主题定义统一的对外接口，用于满足主题数据的使用方的调用需求（也可以使用主题开发框架提供的统一主题服务接口）

#### 主题配置

1）增加包结构
该包用于存储主题服务的相关代码。
在该例中，包路径为：com.sankuai.theme

2）增加xml配置

```xml
<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
xmlns="http://www.springframework.org/schema/beans"
xmlns:athena-theme="http://www.sankuai.com/schema/athena-theme"
xsi:schemaLocation="http://www.springframework.org/schema/beans
http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
http://www.sankuai.com/schema/athena-theme
http://www.sankuai.com/schema/athena-theme-1.0.xsd">
<athena-theme:annotation-driven base-package="com.sankuai.theme"/>//该路径对应上一步创建的主题包
</beans>
```
增加了相关xml后，服务启动Spring容器时，才会自动创建引擎，并上报一系列元数据信息。

3）增加主题配置类
主题配置类，需要放在xml配置的指定包下。

**主题配置类开发规范**

【必须】类必须带有@Theme注解

【必须】注解上必填主题id（id），主题名（name），根节点实体类（root），指定包（basePackage）

【推荐】在配置类下，声明参数，用@Param标记，用于上报参数信息计算，在数据源和文案层可以直接引用。需要填写参数的名称，描述，类型，默认值。

【解释】
+ 主题：每个主题都有一个固定的id，该id需找主题平台负责人分配，如团购主题的id为【100】，主题名为【团购商品主题】。
+ 根节点实体类：每个主题的取数层都有一个取数模型，该取数模型为树状结构，最上层的类也就是根节点，即是根节点实体类，在下文的取数层开发一章中会详细讲解。
+ 指定包：上文提到过的包路径。
+ 参数：主题的传递参数推荐被统一维护起来，所以在该例中写在配置类下面，每一个String的常量代表参数类名，如下图代码为例。

使用 Param 注解声明主题使用的入参，声明后的入参会自动填充转换到上下文中，示例如下：

```java
package com.sankuai.theme;

import com.sankuai.athena.theme.framework.annotations.Param;
import com.sankuai.athena.theme.framework.annotations.Theme;
import com.sankuai.test.dealtheme.model.Deal;
@Theme(id = "100",
        name = "团购商品主题",
        root = Deal.class,
        basePackage = "com.sankuai.theme")
public class DealThemeConfig {

    @Param(name = "dealIds",
            description = "团单列表",
            type = List.class,
            genericTypes = Integer.class,
            defaultValue = "[]")
    public static final String DEAL_IDS = "dealIds";

    @Param(name = "platform",
            description = "平台",
            type = Integer.class,
            defaultValue = "1")
    public static final String PLATFORM = "platform";
}
```

#### 取数层开发

1）数据模型定义

数据模型定义可以类比成表设计，目的是设计数据在主题服务底层的结构是怎么样的，数据模型设计的原则如下：

**数据模型设计原则**
* 可理解性：数据之间的关系，数据字段的命名，应该能够反映真实业务含义
* 易查询性：数据的组织同时要考虑其可查询性，当前的组织方式是否易于查询，主题服务底层的数据模型本质是用来查询的
* 可扩展性：简单的讲就是加字段好加

用户需要设计主题数据的基本数据模型，描述哪些数据可查询，该模型用GraphQL中的schema语法来描述。

在schema的语法中，每一个字段都对应一个Type，Type要么是用户自定义的，要么是基本类型的Type，基本类型包括：String、Boolean、Int、Float、Long、Short、Byte、BigDecimal、BigInteger。对应到数据模型中，就是说实体的基本类型只能是这几种类型，比如Date这种类型是不允许的。

**数据模型开发规范**

【必须】必须存在且只有一个根节点（如团单为Deal）

【必须】类上必须有@Type注解，字段上必须有@Field注解

我们以团单主题为例，设计出来的schema表示可以查询的所有字段。

```java
@Data
@Type(name = "Deal", description = "团单")
public class Deal {
    @Field(name = "dealId", description = "团单ID")
    private int dealId;

    @Field(name = "dealBase", description = "团单基础信息")
    private DealBase dealBase;
    
    @Field(name = "category", description = "团单类目信息")
    private DealCategory category;
}
```

```java
@Data
@Type(name = "DealCategory", description = "团单类目信息")
public class DealCategory {
    @Field(name = "category", description = "团单类目ID")
    private int category;
    
    @Field(name = "categoryName", description = "团单类目名称")
    private String categoryName;
}
```

```java
@Data
@Type(name = "DealBase", description = "团单基础信息")
public class DealBase {

    @Field(name = "dealName", description = "团单名称")
    private String dealName;

    @Field(name = "headPicUrl", description = "团单头图URL")
    private String headPicUrl;

    @Field(name = "marketPrice", description = "原价格")
    private BigDecimal marketPrice;

    @Field(name = "price", description = "团单价格")
    private BigDecimal price;
}
```

```sql
schema {
    query: Query
}
type Query{
    subjects: [Deal]
}
#团单主题
type Deal {
    #团单ID
    dealId : Int
    #团单基础信息
    dealBase : DealBase
    #团单类目信息
    category : DealCategory
}
#团单基础信息
type DealBase {
    #团单名称
    dealName : String
    #团单头图URL
    headPicUrl : String
    #原价格
    marketPrice : BigDecimal
    #团单价格
    price : BigDecimal
}
#团单类目信息
type DealCategory {
    #团单类目ID
    category : Int
    #团单类目名称
    categoryName : String
}
```

2）取数器实现

在设计好主题层数据模型之后，接下来要做的事情是基于标准接口实现数据的获取逻辑，开发框架提供了三个标准的取数接口，主题数据模型中的所有字段都由这三个标准接口的实现组合而来，接下来详述这三个接口：

| Fetcher类型 | 功能及适用场景 |
| --- | --- |
| SingleFetcher | 适用于接受单个ID查询单个对象的场景 |
| MultiFetcher | 适用于接受多个ID查询多个对象的场景 |
| BatcherFetcher | 适用于ID太多，希望分批查询的场景 |

**数据源开发规范**

【必须】必须实现Fetcher（SingleFetcher、MultiFetcher、BatcherFetcher）

【必须】标注@Fetcher注解，声明当前Fetcher需要的数据，数据源名（name），描述（description），对应Schema类型（type），对应字段类型（field），必须参数（params），可选参数（optionalParams），needFields（依赖字段）。

【推荐】推荐使用MultiFetcher和BatchFetcher，分别对应一对多，多对多的查询场景。一般我们不使用SingleFetcher，一对一的查询，经常会引发性能问题。

下面以团单基本信息取数据实现为例。

团单主题schema片段：

```sql
schema {
    query: Query
}
type Query{
    subjects: [Deal]
}
#团单主题
type Deal {
    #团单ID
    dealId : Int
    #团单基础信息
    dealBase : DealBase
}
#团单基础信息
type DealBase {
    #团单标题
    title : String
    #团单头图URL
    dealPicUrl : String
    #原价格
    marketPrice : BigDecimal
    #团单价格
    dealGroupPrice : BigDecimal
}
```
首先介绍MultiFetcher的写法，Deal是根节点，如果我们希望一次查询出一系列的Deal，那么就是一对多（零对多）的查询结构，所以用MultiFetcher比较适合，一般我们默认根节点用MultiFetcher，并且有一些约定的写法。

```sql
schema {
    query: Query
}
type Query{
    subjects: [Deal]
}
#团单主题
type Deal {
    #团单ID
    dealId : Int
}
```

```java
/**
* type = "Query", field = "subjects" 是根节点的Fetcher约定俗成的写法，因为其属于根节点，对应的就是schema中，
* Query 类型下的 subjects 字段
* params 中对应的是配置类中的参数
**/
@Fetcher(name = "DealMultiFetcher",
        description = "团购基本信息",
        type = "Query",
        field = "subjects",
        params = {DealThemeConfig.DEAL_IDS, DealThemeConfig.PLATFORM},
        needFields = {})
public class DealMultiFetcher extends MultiFetcher<Deal> {

  	/**
    * MultiFetcher只需要实现multiGet一个方法，就是如何通过上文传递的数据获取其映射的一批数据
    * fetchingContext.getSource()方法可以获取上个结点的数据，但是本Fetcher属于根节点，所以没有上个节点的数据。
    **/
    @Override
    protected CompletableFuture<List<Deal>> multiGet(FetchingContext fetchingContext) {
      	//获取上下文信息
        ExecutionContext executionContext = fetchingContext.getExecutionContext();
      	//获取参数
        List<Integer> dealIds = fetchingContext.getExecutionContext().getExecution().getParameter(DealThemeConfig.DEAL_IDS);
        int platform = fetchingContext.getExecutionContext().getExecution().getParameter(DealThemeConfig.PLATFORM);
        //拼接结果
      	return convert2DGListFuture(dealIds, platform);
    }

}
```

然后介绍BatchFetcher的写法，团单基本信息DealBase对象由DealBaseBatchFetcher这个取数器查询得到，上个结点的数据为Deal，Deal和DealBase为多对多的关系，所以我们使用BatchFetcher。

因为我们的下游服务接口单次最多接受100个大小的ID列表，因此这里的batchSize为100。

下文代码代表：Deal这个类下的dealBase字段会由DealBaseBatchFetcher来取。

```sql
schema {
    query: Query
}
type Query{
    subjects: [Deal]
}
#团单主题
type Deal {
    #团单ID
    dealId : Int
    #团单基础信息
    dealBase : DealBase
    #团单类目信息
    dealCategory : DealCategory
}
#团单基础信息
type DealBase {
    #团单标题
    title : String
    #团单头图URL
    dealPicUrl : String
    #原价格
    marketPrice : BigDecimal
    #团单价格
    dealGroupPrice : BigDecimal
}
```

fetcher实现
```java
/**
* Deal类下的dealBase字段由DealBaseBatchFetcher来取，故Type为Deal，field为dealBase
* 参数为配置类下的platform
**/
@Fetcher(name = "DealBaseBatchFetcher",
        description = "团单基本信息数据源",
        type = "Deal",
        field = "dealBase",
        params = {DealThemeConfig.PLATFORM},
        needFields = {})
public class DealBaseBatchFetcher extends BatchFetcher<Integer, DealBaseModel> {
  	
  	//代表一次查询多少
    @Override
    public int batchSize() {
        return 100;
    }
  	
  	//代表上一个节点到这个节点的映射关系，本例中为团单Id
    @Override
    public Integer batchKey(FetchingContext fetchingContext) {
        DealModel deal = fetchingContext.getSource();
        return deal.getDealId();
    }
  	
  	//描述通过一批Deal来获取一批DealBase的逻辑
    @Override
    public CompletableFuture<Map<Integer, DealBaseModel>> batchGet(Map<Integer, FetchingContext> map) {
        Set<Integer> dealIdSet = map.keySet();
        CompletableFuture<Map<Integer, DealBaseModel>> dealBaseFutureMap = batchGetDGBase(new ArrayList<>(dealIdSet));
        return dealBaseFutureMap;
    }
}
```

当用户将取数层编辑完成后，服务启动时，主题框架会自动生成GraphQL引擎，建立起schema中描述的数据与具体取数器之间的关系，这部分功能是基于GraphQL开发框架的原生接口RuntimeWiring实现的，具体用法和GraphQL无二义。

#### 3.2.3 文案层开发

数据层开发完之后，接下来就可以基于底层的数据开发文案了，所有的文案开发都必须基于以下规范：

**文案开发规范**

【必须】实现Document接口

【必须】标注@Doc注解，声明当前文案的字段名name和需要的数据needFields，如：@Doc(name = "shopTags", needFields = {"shop"})

下面以团单销量文案为例：

| 展示文案 | 文案生成逻辑 | 所需字段 |
| --- | --- | --- |
| 半年消费XX | 规则一：当销量小于11的时候不展示<br><br>规则二：当销量小于9999的时候展示半年消费${销量}<br><br>规则三：当销量大于9999的时候展示半年消费XX万 | dealNearestShop对象下dealShopSale字段的saleNum字段 |

文案实现代码如下：

```sql
schema {
    query: Query
}
type Query{
    subjects: [Deal]
}
#团单主题
type Deal {
    #团单ID
    dealId : Int

    #团单最近商户信息
    dealNearestShop : DealNearestShop
}
#团单最近商户信息
type DealNearestShop {
    #团单ID
    dealId:Int
  
    #团单在该商户下的售卖信息
    dealShopSale : DealShopSale
}

type DealShopSale{
    #团单在该商户下销量
    saleNum : Int

    #团单在该商户下销量文案
    saleNumStr:String
}
```
对应代码
```java
@Doc(name = "shopSaleText", 
     description="团单销量文案",
     //needField需要声明：我需要数据模型的哪些字段，框架会基于这个声明反向推导需要调用哪些接口
     //字段名的拼接是用'.'将节点串联起来，根节点缺省
     //该字段的含义是，取Deal下面的dealNearestShop中的dealShopSale中的saleNum值
     needFields = {"dealNearestShop.dealShopSale.saleNum"},
     params = {},//需要参数
     optionalParams = {} //可选参数
     )
public class DealShopSaleDocument implements Document<String> {

    private static int MIN_DISPLAY_NUM = 11;

    @Override
    public String execute(DocContext docContext) {
        Record subject = docContext.getSubject();
				//获取依赖字段值
      	Integer saleNum = subject.getField("dealNearestShop.dealShopSale.saleNum");
        if (saleNum == null) {
            return null;
        }
        if (saleNum < MIN_DISPLAY_NUM) {
            return null;
        }
        return buildShopSaleText(saleNum);
    }

    private String buildShopSaleText(Integer saleNum) {
        if (saleNum < 9999) {
            return "半年消费" + saleNum;
        }
        BigDecimal dividend = BigDecimal.valueOf(saleNum);
        BigDecimal divisor = BigDecimal.valueOf(10000);
        BigDecimal divide = dividend.divide(divisor, 1, BigDecimal.ROUND_HALF_DOWN);
        return "半年消费" + divide.doubleValue() + "万";
    }
}
```

#### 3.2.3 主题接口开发

1）主题接口定义

主题接口可以由主题开发者自己定义，但是必须满足主题接口定义契约：

**主题接口定义契约**【必须】planId参数，该参数用于传入主题开发框架调用接口，planId本质上关联的是主题开发平台中的一套配置，该配置描述的是：读取哪些文案？采用怎么样的排序策略？

【必须】主题实例ID列表（subjects），该参数是读取主题文案的核心参数

【可选】扩展参数，如deviceId，lat、lng等参数

2）主题接口开发

以上步骤完成之后，就可以基于主题开发框架提供的接口按需查询主题文案了，但是此时只能本地调用，主题需要开发一段代码，让外部可以调用主题的能力

```java
// 1.注入引擎，主题引擎为Spring自动注入，名称为{主题ID.PlanEngine}
@SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
@Autowired
@Qualifier("100.PlanEngine")
private PlanEngine planEngine;

// 2. 构造引擎查询参数，对应主题配置中的参数
Map<String, Object> paramMap = buildParams(dealIds, params);
PlanRequest planRequest = buildPlanRequest(planId, paramMap);
// 3. 执行引擎进行主题数据的查询
CompletableFuture<PlanResponse> planResponseCompletableFuture = planEngine.execute(planRequest);
// 4. 将引擎返回的数据转换成主题服务接口定义对象
// 此处省略

private Map<String, Object> buildParams(List<Integer> dealIds, Map<String, Object> params) {
  Map<String, Object> map = Maps.newHashMap();
  for (Map.Entry<String, Object> entry : params.entrySet()) {
    map.put(entry.getKey(), entry.getValue());
  }
  map.put(ContextUtils.DEAL_IDS, dealIds);
  return map;
}

private PlanRequest buildPlanRequest(String planId, Map<String, Object> params) {
  PlanRequest planRequest = new PlanRequest();
  planRequest.setPlanId(planId);
  planRequest.setParams(params);
  return planRequest;
}
```